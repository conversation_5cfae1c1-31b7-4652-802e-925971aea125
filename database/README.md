# Mufasa Multistore Platform Database Schema

This directory contains the complete database schema for a Yelp-like multistore platform built with Supabase.

## 🗂️ Files Overview

1. **01_create_tables.sql** - Core tables (users, stores, products, categories)
2. **02_reviews_orders_tables.sql** - Reviews, orders, carts, and related tables
3. **03_indexes_triggers_rls.sql** - Performance indexes, triggers, and RLS setup
4. **04_rls_policies.sql** - Row Level Security policies for data access control
5. **05_sample_data.sql** - Sample data, utility functions, and views

## 🚀 Setup Instructions

### 1. Create a New Supabase Project

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Click "New Project"
3. Choose your organization and enter project details
4. Wait for the project to be created

### 2. Run the SQL Scripts

Execute the SQL files in order in your Supabase SQL Editor:

1. **01_create_tables.sql** - Creates all core tables
2. **02_reviews_orders_tables.sql** - Creates review and order tables
3. **03_indexes_triggers_rls.sql** - Sets up indexes, triggers, and enables RLS
4. **04_rls_policies.sql** - Creates all RLS policies
5. **05_sample_data.sql** - Adds sample data and utility functions

### 3. Configure Authentication

In your Supabase dashboard:

1. Go to **Authentication > Settings**
2. Enable email confirmation if desired
3. Configure any social providers (Google, GitHub, etc.)
4. Set up custom SMTP if needed

### 4. Set Up Storage (Optional)

For file uploads (product images, store logos):

1. Go to **Storage**
2. Create buckets for:
   - `store-logos`
   - `store-covers`
   - `product-images`
   - `user-avatars`
3. Set appropriate policies for each bucket

## 📊 Database Schema Overview

### Core Entities

- **Users** - Customer and store owner profiles
- **Stores** - Individual stores/businesses
- **Categories** - Hierarchical product categories
- **Products** - Items sold by stores
- **Product Variants** - Different options (size, color, etc.)

### E-commerce Features

- **Orders** - Customer orders with full order management
- **Cart** - Shopping cart functionality
- **Reviews** - Store and product reviews with ratings
- **Coupons** - Discount codes and promotions
- **Favorites** - User wishlist functionality

### Key Features

- **Hierarchical Categories** - Support for nested categories
- **Multi-variant Products** - Products with different options
- **Location-based Search** - Find stores by geographic location
- **Review System** - Comprehensive rating and review system
- **Order Management** - Full order lifecycle tracking
- **Role-based Access** - Customer, store owner, and admin roles

## 🔐 Security Features

### Row Level Security (RLS)

All tables have RLS enabled with comprehensive policies:

- **Users** can only access their own data
- **Store owners** can manage their own stores and products
- **Customers** can view public data and manage their own orders/reviews
- **Admins** have full access to manage the platform

### Data Validation

- Email uniqueness constraints
- Rating validation (1-5 stars)
- Order status validation
- Price and quantity constraints

## 🔍 Useful Queries

### Find Stores Near Location

```sql
SELECT * FROM search_stores_by_location(40.7128, -74.0060, 10);
```

### Get Store Statistics

```sql
SELECT * FROM get_store_stats('store-uuid-here');
```

### Search Products

```sql
SELECT * FROM search_products('pizza');
```

### Get Store Listings with Categories

```sql
SELECT * FROM store_listings WHERE city = 'New York';
```

## 📱 API Integration

This schema is designed to work with:

- **Supabase Client Libraries** (JavaScript, Python, etc.)
- **REST API** - Auto-generated by Supabase
- **GraphQL** - Available through Supabase
- **Real-time subscriptions** - For live updates

## 🛠️ Customization

### Adding New Fields

To add new fields to existing tables:

1. Add the column using ALTER TABLE
2. Update relevant RLS policies if needed
3. Update any affected views or functions

### Adding New Tables

1. Create the table with appropriate constraints
2. Add indexes for performance
3. Enable RLS and create policies
4. Update any related functions or views

## 📈 Performance Considerations

### Indexes

The schema includes optimized indexes for:

- Search queries (full-text search)
- Location-based queries (geographic indexes)
- Foreign key relationships
- Common filter conditions

### Triggers

Automatic triggers handle:

- Updated timestamp maintenance
- Rating calculations
- Order number generation

## 🧪 Testing

### Sample Data

The `05_sample_data.sql` file includes:

- Sample categories (Food, Electronics, Fashion, etc.)
- Utility functions for common operations
- Views for simplified data access

### Test Scenarios

1. **User Registration** - Test user signup and profile creation
2. **Store Creation** - Test store owner registration and store setup
3. **Product Management** - Test product CRUD operations
4. **Order Flow** - Test complete order lifecycle
5. **Review System** - Test review creation and rating updates

## 🔧 Maintenance

### Regular Tasks

1. **Monitor Performance** - Check slow queries and optimize indexes
2. **Review RLS Policies** - Ensure security policies are working correctly
3. **Backup Data** - Regular database backups
4. **Update Statistics** - Keep database statistics current

### Monitoring Queries

```sql
-- Check table sizes
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables WHERE schemaname = 'public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check index usage
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats WHERE schemaname = 'public';
```

## 📞 Support

For issues or questions:

1. Check the Supabase documentation
2. Review the SQL comments in each file
3. Test queries in the Supabase SQL Editor
4. Use the Supabase community forums

## 🎯 Next Steps

After setting up the database:

1. **Create your backend API** - Use your preferred framework
2. **Set up authentication** - Implement user registration/login
3. **Build the frontend** - Create your web/mobile application
4. **Test thoroughly** - Ensure all features work correctly
5. **Deploy** - Launch your multistore platform

---

**Note**: Remember to replace placeholder UUIDs in sample data with actual user IDs from your auth.users table.
