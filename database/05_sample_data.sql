-- Sample Data and Utility Functions for Mufasa Multistore Platform
-- Run this after all previous SQL files

-- =============================================
-- UTILITY FUNCTIONS
-- =============================================

-- Function to search stores by location (within radius)
CREATE OR REPLACE FUNCTION search_stores_by_location(
    search_lat DECIMAL(10, 8),
    search_lng DECIMAL(11, 8),
    radius_km INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    slug TEXT,
    description TEXT,
    logo_url TEXT,
    address_line1 TEXT,
    city TEXT,
    state TEXT,
    average_rating DECIMAL(3, 2),
    total_reviews INTEGER,
    distance_km DECIMAL(10, 2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id,
        s.name,
        s.slug,
        s.description,
        s.logo_url,
        s.address_line1,
        s.city,
        s.state,
        s.average_rating,
        s.total_reviews,
        ROUND(
            (6371 * acos(
                cos(radians(search_lat)) * 
                cos(radians(s.latitude)) * 
                cos(radians(s.longitude) - radians(search_lng)) + 
                sin(radians(search_lat)) * 
                sin(radians(s.latitude))
            ))::numeric, 2
        ) AS distance_km
    FROM public.stores s
    WHERE 
        s.status = 'active' 
        AND s.latitude IS NOT NULL 
        AND s.longitude IS NOT NULL
        AND (
            6371 * acos(
                cos(radians(search_lat)) * 
                cos(radians(s.latitude)) * 
                cos(radians(s.longitude) - radians(search_lng)) + 
                sin(radians(search_lat)) * 
                sin(radians(s.latitude))
            )
        ) <= radius_km
    ORDER BY distance_km;
END;
$$ LANGUAGE plpgsql;

-- Function to get store statistics
CREATE OR REPLACE FUNCTION get_store_stats(store_uuid UUID)
RETURNS TABLE (
    total_products INTEGER,
    total_orders INTEGER,
    total_revenue DECIMAL(12, 2),
    average_rating DECIMAL(3, 2),
    total_reviews INTEGER,
    total_followers INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*)::INTEGER FROM public.products WHERE store_id = store_uuid AND is_active = true),
        (SELECT COUNT(*)::INTEGER FROM public.orders WHERE store_id = store_uuid),
        (SELECT COALESCE(SUM(total_amount), 0) FROM public.orders WHERE store_id = store_uuid AND status = 'delivered'),
        s.average_rating,
        s.total_reviews,
        (SELECT COUNT(*)::INTEGER FROM public.store_followers WHERE store_id = store_uuid)
    FROM public.stores s
    WHERE s.id = store_uuid;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- SAMPLE CATEGORIES
-- =============================================

INSERT INTO public.categories (name, slug, description, image_url, sort_order) VALUES
('Food & Beverages', 'food-beverages', 'Restaurants, cafes, and food delivery', 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445', 1),
('Electronics', 'electronics', 'Phones, computers, and electronic devices', 'https://images.unsplash.com/photo-1498049794561-7780e7231661', 2),
('Fashion & Clothing', 'fashion-clothing', 'Clothing, shoes, and accessories', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8', 3),
('Health & Beauty', 'health-beauty', 'Cosmetics, skincare, and wellness products', 'https://images.unsplash.com/photo-1596462502278-27bfdc403348', 4),
('Home & Garden', 'home-garden', 'Furniture, decor, and gardening supplies', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7', 5),
('Sports & Fitness', 'sports-fitness', 'Sporting goods and fitness equipment', 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b', 6),
('Books & Media', 'books-media', 'Books, movies, music, and games', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570', 7),
('Automotive', 'automotive', 'Car parts, accessories, and services', 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000', 8);

-- Food subcategories
INSERT INTO public.categories (name, slug, description, parent_id, sort_order) VALUES
('Pizza', 'pizza', 'Pizza restaurants and delivery', (SELECT id FROM public.categories WHERE slug = 'food-beverages'), 1),
('Chinese', 'chinese', 'Chinese cuisine', (SELECT id FROM public.categories WHERE slug = 'food-beverages'), 2),
('Italian', 'italian', 'Italian restaurants', (SELECT id FROM public.categories WHERE slug = 'food-beverages'), 3),
('Fast Food', 'fast-food', 'Quick service restaurants', (SELECT id FROM public.categories WHERE slug = 'food-beverages'), 4),
('Coffee & Tea', 'coffee-tea', 'Coffee shops and tea houses', (SELECT id FROM public.categories WHERE slug = 'food-beverages'), 5),
('Bakery', 'bakery', 'Bakeries and pastry shops', (SELECT id FROM public.categories WHERE slug = 'food-beverages'), 6);

-- Electronics subcategories
INSERT INTO public.categories (name, slug, description, parent_id, sort_order) VALUES
('Smartphones', 'smartphones', 'Mobile phones and accessories', (SELECT id FROM public.categories WHERE slug = 'electronics'), 1),
('Laptops', 'laptops', 'Laptops and notebooks', (SELECT id FROM public.categories WHERE slug = 'electronics'), 2),
('Gaming', 'gaming', 'Gaming consoles and accessories', (SELECT id FROM public.categories WHERE slug = 'electronics'), 3),
('Audio', 'audio', 'Headphones, speakers, and audio equipment', (SELECT id FROM public.categories WHERE slug = 'electronics'), 4);

-- =============================================
-- SAMPLE STORES (You'll need to create users first)
-- =============================================

-- Note: These INSERT statements will need actual user IDs from your auth.users table
-- For now, I'll create placeholder stores that you can update with real user IDs

-- Sample store data (replace owner_id with actual user UUIDs)
INSERT INTO public.stores (
    owner_id, name, slug, description, logo_url, cover_image_url,
    phone, email, website_url,
    address_line1, city, state, postal_code, country,
    latitude, longitude,
    business_hours, delivery_radius, minimum_order_amount, delivery_fee,
    status
) VALUES
(
    '00000000-0000-0000-0000-000000000001', -- Replace with actual user ID
    'Mario''s Pizza Palace',
    'marios-pizza-palace',
    'Authentic Italian pizza made with fresh ingredients and traditional recipes.',
    'https://images.unsplash.com/photo-1513104890138-7c749659a591',
    'https://images.unsplash.com/photo-1571997478779-2adcbbe9ab2f',
    '******-0123',
    '<EMAIL>',
    'https://mariospizza.com',
    '123 Main Street',
    'New York',
    'NY',
    '10001',
    'US',
    40.7128,
    -74.0060,
    '{"monday": {"open": "11:00", "close": "23:00"}, "tuesday": {"open": "11:00", "close": "23:00"}, "wednesday": {"open": "11:00", "close": "23:00"}, "thursday": {"open": "11:00", "close": "23:00"}, "friday": {"open": "11:00", "close": "24:00"}, "saturday": {"open": "11:00", "close": "24:00"}, "sunday": {"open": "12:00", "close": "22:00"}}',
    5,
    15.00,
    2.99,
    'active'
);


-- =============================================
-- USEFUL VIEWS
-- =============================================

-- View for store listings with category information
CREATE OR REPLACE VIEW store_listings AS
SELECT 
    s.id,
    s.name,
    s.slug,
    s.description,
    s.logo_url,
    s.cover_image_url,
    s.address_line1,
    s.city,
    s.state,
    s.postal_code,
    s.latitude,
    s.longitude,
    s.average_rating,
    s.total_reviews,
    s.minimum_order_amount,
    s.delivery_fee,
    s.delivery_radius,
    s.business_hours,
    s.created_at,
    ARRAY_AGG(DISTINCT c.name) as categories,
    ARRAY_AGG(DISTINCT c.slug) as category_slugs
FROM public.stores s
LEFT JOIN public.store_categories sc ON s.id = sc.store_id
LEFT JOIN public.categories c ON sc.category_id = c.id
WHERE s.status = 'active'
GROUP BY s.id, s.name, s.slug, s.description, s.logo_url, s.cover_image_url,
         s.address_line1, s.city, s.state, s.postal_code, s.latitude, s.longitude,
         s.average_rating, s.total_reviews, s.minimum_order_amount, s.delivery_fee,
         s.delivery_radius, s.business_hours, s.created_at;

-- View for product listings with store and category information
CREATE OR REPLACE VIEW product_listings AS
SELECT 
    p.id,
    p.name,
    p.slug,
    p.description,
    p.short_description,
    p.price,
    p.compare_at_price,
    p.images,
    p.average_rating,
    p.total_reviews,
    p.is_featured,
    p.created_at,
    s.id as store_id,
    s.name as store_name,
    s.slug as store_slug,
    s.logo_url as store_logo,
    c.id as category_id,
    c.name as category_name,
    c.slug as category_slug
FROM public.products p
JOIN public.stores s ON p.store_id = s.id
LEFT JOIN public.categories c ON p.category_id = c.id
WHERE p.is_active = true AND s.status = 'active';

-- View for order summaries
CREATE OR REPLACE VIEW order_summaries AS
SELECT 
    o.id,
    o.order_number,
    o.status,
    o.customer_name,
    o.customer_email,
    o.total_amount,
    o.created_at,
    s.name as store_name,
    s.slug as store_slug,
    COUNT(oi.id) as item_count,
    SUM(oi.quantity) as total_quantity
FROM public.orders o
JOIN public.stores s ON o.store_id = s.id
LEFT JOIN public.order_items oi ON o.id = oi.order_id
GROUP BY o.id, o.order_number, o.status, o.customer_name, o.customer_email,
         o.total_amount, o.created_at, s.name, s.slug;

-- =============================================
-- SEARCH FUNCTIONS
-- =============================================

-- Full-text search for products
CREATE OR REPLACE FUNCTION search_products(search_term TEXT, category_filter UUID DEFAULT NULL, store_filter UUID DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    name TEXT,
    slug TEXT,
    description TEXT,
    price DECIMAL(10, 2),
    images JSONB,
    average_rating DECIMAL(3, 2),
    store_name TEXT,
    store_slug TEXT,
    category_name TEXT,
    rank REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.slug,
        p.description,
        p.price,
        p.images,
        p.average_rating,
        s.name as store_name,
        s.slug as store_slug,
        c.name as category_name,
        ts_rank(
            to_tsvector('english', p.name || ' ' || COALESCE(p.description, '') || ' ' || COALESCE(p.short_description, '')),
            plainto_tsquery('english', search_term)
        ) as rank
    FROM public.products p
    JOIN public.stores s ON p.store_id = s.id
    LEFT JOIN public.categories c ON p.category_id = c.id
    WHERE 
        p.is_active = true 
        AND s.status = 'active'
        AND (
            to_tsvector('english', p.name || ' ' || COALESCE(p.description, '') || ' ' || COALESCE(p.short_description, ''))
            @@ plainto_tsquery('english', search_term)
        )
        AND (category_filter IS NULL OR p.category_id = category_filter)
        AND (store_filter IS NULL OR p.store_id = store_filter)
    ORDER BY rank DESC, p.average_rating DESC, p.created_at DESC;
END;
$$ LANGUAGE plpgsql;
