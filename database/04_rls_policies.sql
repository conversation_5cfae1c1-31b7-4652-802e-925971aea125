-- Row Level Security Policies for Mufasa Multistore Platform
-- Run this after 03_indexes_triggers_rls.sql

-- =============================================
-- USERS TABLE POLICIES
-- =============================================

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Allow user creation during signup
CREATE POLICY "Enable insert for authentication" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- =============================================
-- CATEGORIES TABLE POLICIES
-- =============================================

-- Everyone can view active categories
CREATE POLICY "Anyone can view active categories" ON public.categories
    FOR SELECT USING (is_active = true);

-- Only admins can manage categories
CREATE POLICY "Only admins can manage categories" ON public.categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- STORES TABLE POLICIES
-- =============================================

-- Everyone can view active stores
CREATE POLICY "Anyone can view active stores" ON public.stores
    FOR SELECT USING (status = 'active');

-- Store owners can view their own stores
CREATE POLICY "Store owners can view own stores" ON public.stores
    FOR SELECT USING (owner_id = auth.uid());

-- Store owners can create stores
CREATE POLICY "Store owners can create stores" ON public.stores
    FOR INSERT WITH CHECK (
        auth.uid() = owner_id AND
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('store_owner', 'admin')
        )
    );

-- Store owners can update their own stores
CREATE POLICY "Store owners can update own stores" ON public.stores
    FOR UPDATE USING (owner_id = auth.uid());

-- Admins can manage all stores
CREATE POLICY "Admins can manage all stores" ON public.stores
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- STORE CATEGORIES POLICIES
-- =============================================

-- Everyone can view store categories
CREATE POLICY "Anyone can view store categories" ON public.store_categories
    FOR SELECT USING (true);

-- Store owners can manage their store categories
CREATE POLICY "Store owners can manage own store categories" ON public.store_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.stores 
            WHERE id = store_id AND owner_id = auth.uid()
        )
    );

-- =============================================
-- PRODUCTS TABLE POLICIES
-- =============================================

-- Everyone can view active products from active stores
CREATE POLICY "Anyone can view active products" ON public.products
    FOR SELECT USING (
        is_active = true AND
        EXISTS (
            SELECT 1 FROM public.stores 
            WHERE id = store_id AND status = 'active'
        )
    );

-- Store owners can view their own products
CREATE POLICY "Store owners can view own products" ON public.products
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.stores 
            WHERE id = store_id AND owner_id = auth.uid()
        )
    );

-- Store owners can manage their own products
CREATE POLICY "Store owners can manage own products" ON public.products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.stores 
            WHERE id = store_id AND owner_id = auth.uid()
        )
    );

-- =============================================
-- PRODUCT VARIANTS POLICIES
-- =============================================

-- Everyone can view variants of active products
CREATE POLICY "Anyone can view active product variants" ON public.product_variants
    FOR SELECT USING (
        is_active = true AND
        EXISTS (
            SELECT 1 FROM public.products p
            JOIN public.stores s ON p.store_id = s.id
            WHERE p.id = product_id AND p.is_active = true AND s.status = 'active'
        )
    );

-- Store owners can manage variants of their products
CREATE POLICY "Store owners can manage own product variants" ON public.product_variants
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.products p
            JOIN public.stores s ON p.store_id = s.id
            WHERE p.id = product_id AND s.owner_id = auth.uid()
        )
    );

-- =============================================
-- PRODUCT OPTIONS POLICIES
-- =============================================

-- Everyone can view product options
CREATE POLICY "Anyone can view product options" ON public.product_options
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.products p
            JOIN public.stores s ON p.store_id = s.id
            WHERE p.id = product_id AND p.is_active = true AND s.status = 'active'
        )
    );

-- Store owners can manage their product options
CREATE POLICY "Store owners can manage own product options" ON public.product_options
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.products p
            JOIN public.stores s ON p.store_id = s.id
            WHERE p.id = product_id AND s.owner_id = auth.uid()
        )
    );

-- =============================================
-- PRODUCT OPTION VALUES POLICIES
-- =============================================

-- Everyone can view product option values
CREATE POLICY "Anyone can view product option values" ON public.product_option_values
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.product_options po
            JOIN public.products p ON po.product_id = p.id
            JOIN public.stores s ON p.store_id = s.id
            WHERE po.id = option_id AND p.is_active = true AND s.status = 'active'
        )
    );

-- Store owners can manage their product option values
CREATE POLICY "Store owners can manage own product option values" ON public.product_option_values
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.product_options po
            JOIN public.products p ON po.product_id = p.id
            JOIN public.stores s ON p.store_id = s.id
            WHERE po.id = option_id AND s.owner_id = auth.uid()
        )
    );

-- =============================================
-- VARIANT OPTION VALUES POLICIES
-- =============================================

-- Everyone can view variant option values
CREATE POLICY "Anyone can view variant option values" ON public.variant_option_values
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.product_variants pv
            JOIN public.products p ON pv.product_id = p.id
            JOIN public.stores s ON p.store_id = s.id
            WHERE pv.id = variant_id AND pv.is_active = true AND p.is_active = true AND s.status = 'active'
        )
    );

-- Store owners can manage their variant option values
CREATE POLICY "Store owners can manage own variant option values" ON public.variant_option_values
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.product_variants pv
            JOIN public.products p ON pv.product_id = p.id
            JOIN public.stores s ON p.store_id = s.id
            WHERE pv.id = variant_id AND s.owner_id = auth.uid()
        )
    );

-- =============================================
-- REVIEWS POLICIES
-- =============================================

-- Everyone can view approved store reviews
CREATE POLICY "Anyone can view approved store reviews" ON public.store_reviews
    FOR SELECT USING (is_approved = true);

-- Users can create store reviews
CREATE POLICY "Users can create store reviews" ON public.store_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own store reviews
CREATE POLICY "Users can update own store reviews" ON public.store_reviews
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own store reviews
CREATE POLICY "Users can delete own store reviews" ON public.store_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- Everyone can view approved product reviews
CREATE POLICY "Anyone can view approved product reviews" ON public.product_reviews
    FOR SELECT USING (is_approved = true);

-- Users can create product reviews
CREATE POLICY "Users can create product reviews" ON public.product_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own product reviews
CREATE POLICY "Users can update own product reviews" ON public.product_reviews
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own product reviews
CREATE POLICY "Users can delete own product reviews" ON public.product_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- =============================================
-- CUSTOMER ADDRESSES POLICIES
-- =============================================

-- Users can manage their own addresses
CREATE POLICY "Users can manage own addresses" ON public.customer_addresses
    FOR ALL USING (auth.uid() = user_id);

-- =============================================
-- CART POLICIES
-- =============================================

-- Users can manage their own carts
CREATE POLICY "Users can manage own carts" ON public.carts
    FOR ALL USING (auth.uid() = user_id);

-- Guest users can manage carts by session
CREATE POLICY "Guest users can manage carts by session" ON public.carts
    FOR ALL USING (session_id IS NOT NULL AND user_id IS NULL);

-- Users can manage their own cart items
CREATE POLICY "Users can manage own cart items" ON public.cart_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.carts 
            WHERE id = cart_id AND user_id = auth.uid()
        )
    );

-- =============================================
-- ORDERS POLICIES
-- =============================================

-- Users can view their own orders
CREATE POLICY "Users can view own orders" ON public.orders
    FOR SELECT USING (auth.uid() = user_id);

-- Store owners can view orders for their stores
CREATE POLICY "Store owners can view store orders" ON public.orders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.stores 
            WHERE id = store_id AND owner_id = auth.uid()
        )
    );

-- Users can create orders
CREATE POLICY "Users can create orders" ON public.orders
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Store owners can update orders for their stores
CREATE POLICY "Store owners can update store orders" ON public.orders
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.stores 
            WHERE id = store_id AND owner_id = auth.uid()
        )
    );

-- Order items follow the same pattern as orders
CREATE POLICY "Users can view own order items" ON public.order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.orders 
            WHERE id = order_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Store owners can view store order items" ON public.order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.orders o
            JOIN public.stores s ON o.store_id = s.id
            WHERE o.id = order_id AND s.owner_id = auth.uid()
        )
    );

-- =============================================
-- FAVORITES POLICIES
-- =============================================

-- Users can manage their own favorites
CREATE POLICY "Users can manage own favorites" ON public.favorites
    FOR ALL USING (auth.uid() = user_id);

-- =============================================
-- STORE FOLLOWERS POLICIES
-- =============================================

-- Users can manage their own store follows
CREATE POLICY "Users can manage own store follows" ON public.store_followers
    FOR ALL USING (auth.uid() = user_id);

-- Everyone can view store follower counts (but not individual followers)
CREATE POLICY "Anyone can view store follower counts" ON public.store_followers
    FOR SELECT USING (true);

-- =============================================
-- COUPONS POLICIES
-- =============================================

-- Everyone can view active coupons
CREATE POLICY "Anyone can view active coupons" ON public.coupons
    FOR SELECT USING (is_active = true AND (expires_at IS NULL OR expires_at > NOW()));

-- Store owners can manage their own coupons
CREATE POLICY "Store owners can manage own coupons" ON public.coupons
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.stores 
            WHERE id = store_id AND owner_id = auth.uid()
        )
    );
