-- Indexes, Triggers, and Row Level Security for Mufasa Multistore Platform
-- Run this after 01_create_tables.sql and 02_reviews_orders_tables.sql

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Users indexes
CREATE INDEX idx_users_role ON public.users(role);
CREATE INDEX idx_users_email ON public.users(email);

-- Categories indexes
CREATE INDEX idx_categories_parent_id ON public.categories(parent_id);
CREATE INDEX idx_categories_slug ON public.categories(slug);
CREATE INDEX idx_categories_active ON public.categories(is_active);

-- Stores indexes
CREATE INDEX idx_stores_owner_id ON public.stores(owner_id);
CREATE INDEX idx_stores_slug ON public.stores(slug);
CREATE INDEX idx_stores_status ON public.stores(status);
CREATE INDEX idx_stores_location ON public.stores(latitude, longitude);
CREATE INDEX idx_stores_city ON public.stores(city);
CREATE INDEX idx_stores_rating ON public.stores(average_rating);

-- Store categories indexes
CREATE INDEX idx_store_categories_store_id ON public.store_categories(store_id);
CREATE INDEX idx_store_categories_category_id ON public.store_categories(category_id);

-- Products indexes
CREATE INDEX idx_products_store_id ON public.products(store_id);
CREATE INDEX idx_products_category_id ON public.products(category_id);
CREATE INDEX idx_products_slug ON public.products(slug);
CREATE INDEX idx_products_active ON public.products(is_active);
CREATE INDEX idx_products_featured ON public.products(is_featured);
CREATE INDEX idx_products_price ON public.products(price);
CREATE INDEX idx_products_rating ON public.products(average_rating);
CREATE INDEX idx_products_created_at ON public.products(created_at);

-- Product variants indexes
CREATE INDEX idx_product_variants_product_id ON public.product_variants(product_id);
CREATE INDEX idx_product_variants_sku ON public.product_variants(sku);

-- Reviews indexes
CREATE INDEX idx_store_reviews_store_id ON public.store_reviews(store_id);
CREATE INDEX idx_store_reviews_user_id ON public.store_reviews(user_id);
CREATE INDEX idx_store_reviews_rating ON public.store_reviews(rating);
CREATE INDEX idx_store_reviews_approved ON public.store_reviews(is_approved);

CREATE INDEX idx_product_reviews_product_id ON public.product_reviews(product_id);
CREATE INDEX idx_product_reviews_user_id ON public.product_reviews(user_id);
CREATE INDEX idx_product_reviews_rating ON public.product_reviews(rating);
CREATE INDEX idx_product_reviews_approved ON public.product_reviews(is_approved);

-- Orders indexes
CREATE INDEX idx_orders_user_id ON public.orders(user_id);
CREATE INDEX idx_orders_store_id ON public.orders(store_id);
CREATE INDEX idx_orders_status ON public.orders(status);
CREATE INDEX idx_orders_created_at ON public.orders(created_at);
CREATE INDEX idx_orders_order_number ON public.orders(order_number);

-- Cart indexes
CREATE INDEX idx_carts_user_id ON public.carts(user_id);
CREATE INDEX idx_carts_session_id ON public.carts(session_id);
CREATE INDEX idx_carts_store_id ON public.carts(store_id);

-- =============================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON public.categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_stores_updated_at BEFORE UPDATE ON public.stores FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON public.products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_product_variants_updated_at BEFORE UPDATE ON public.product_variants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_store_reviews_updated_at BEFORE UPDATE ON public.store_reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_product_reviews_updated_at BEFORE UPDATE ON public.product_reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON public.orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_coupons_updated_at BEFORE UPDATE ON public.coupons FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update store rating when reviews are added/updated/deleted
CREATE OR REPLACE FUNCTION update_store_rating()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        UPDATE public.stores 
        SET 
            average_rating = COALESCE((
                SELECT ROUND(AVG(rating)::numeric, 2) 
                FROM public.store_reviews 
                WHERE store_id = OLD.store_id AND is_approved = true
            ), 0),
            total_reviews = COALESCE((
                SELECT COUNT(*) 
                FROM public.store_reviews 
                WHERE store_id = OLD.store_id AND is_approved = true
            ), 0)
        WHERE id = OLD.store_id;
        RETURN OLD;
    ELSE
        UPDATE public.stores 
        SET 
            average_rating = COALESCE((
                SELECT ROUND(AVG(rating)::numeric, 2) 
                FROM public.store_reviews 
                WHERE store_id = NEW.store_id AND is_approved = true
            ), 0),
            total_reviews = COALESCE((
                SELECT COUNT(*) 
                FROM public.store_reviews 
                WHERE store_id = NEW.store_id AND is_approved = true
            ), 0)
        WHERE id = NEW.store_id;
        RETURN NEW;
    END IF;
END;
$$ language 'plpgsql';

-- Function to update product rating when reviews are added/updated/deleted
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        UPDATE public.products 
        SET 
            average_rating = COALESCE((
                SELECT ROUND(AVG(rating)::numeric, 2) 
                FROM public.product_reviews 
                WHERE product_id = OLD.product_id AND is_approved = true
            ), 0),
            total_reviews = COALESCE((
                SELECT COUNT(*) 
                FROM public.product_reviews 
                WHERE product_id = OLD.product_id AND is_approved = true
            ), 0)
        WHERE id = OLD.product_id;
        RETURN OLD;
    ELSE
        UPDATE public.products 
        SET 
            average_rating = COALESCE((
                SELECT ROUND(AVG(rating)::numeric, 2) 
                FROM public.product_reviews 
                WHERE product_id = NEW.product_id AND is_approved = true
            ), 0),
            total_reviews = COALESCE((
                SELECT COUNT(*) 
                FROM public.product_reviews 
                WHERE product_id = NEW.product_id AND is_approved = true
            ), 0)
        WHERE id = NEW.product_id;
        RETURN NEW;
    END IF;
END;
$$ language 'plpgsql';

-- Apply rating update triggers
CREATE TRIGGER trigger_update_store_rating 
    AFTER INSERT OR UPDATE OR DELETE ON public.store_reviews 
    FOR EACH ROW EXECUTE FUNCTION update_store_rating();

CREATE TRIGGER trigger_update_product_rating 
    AFTER INSERT OR UPDATE OR DELETE ON public.product_reviews 
    FOR EACH ROW EXECUTE FUNCTION update_product_rating();

-- Function to generate order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.order_number = 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(nextval('order_number_seq')::text, 6, '0');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for order numbers
CREATE SEQUENCE IF NOT EXISTS order_number_seq START 1;

-- Apply order number trigger
CREATE TRIGGER trigger_generate_order_number 
    BEFORE INSERT ON public.orders 
    FOR EACH ROW EXECUTE FUNCTION generate_order_number();

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.store_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_options ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_option_values ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.variant_option_values ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.store_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_helpfulness ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.store_followers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.coupons ENABLE ROW LEVEL SECURITY;
